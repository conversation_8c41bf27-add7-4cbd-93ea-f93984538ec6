# 请在dify项目原有的 nginx.conf 文件上进行修改

server {
    listen ${NGINX_PORT};
    server_name ${NGINX_SERVER_NAME};

    # ================= 新增部分 开始 =================
    # 专门处理包含 token 参数的自动登录请求
    # 匹配所有包含 token、access_token 或 refresh_token 的请求
    location ~ ^/dify/.*[?&](token|access_token|refresh_token)= {
        # 对于包含 token 的请求，直接代理到前端，保留完整的查询参数
        # 使用 $1 捕获 /dify/ 后的所有内容，确保查询参数完整传递
        rewrite ^/dify/(.*)$ /$1 break;
        proxy_pass http://web:3000;
        include proxy.conf;

        # 添加额外的头部信息，帮助调试
        proxy_set_header X-Original-URI $request_uri;
        proxy_set_header X-Rewritten-URI /$1;
    }

    # 专门处理自动登录相关的 API 请求
    location ~ ^/dify/(console/api/token-login|console/api/refresh-token|api/passport) {
        rewrite ^/dify/(.*)$ /$1 break;
        proxy_pass http://api:5001;
        include proxy.conf;

        # 确保 API 请求的头部信息正确传递
        proxy_set_header X-Original-URI $request_uri;
    }

    # 处理根路径的自动登录（当 URL 是 /dify/?token=xxx 的情况）
    location = /dify/ {
        # 如果查询参数包含 token，直接代理到前端
        if ($args ~ "(token|access_token|refresh_token)=") {
            rewrite ^/dify/$ / break;
            proxy_pass http://web:3000;
            include proxy.conf;
        }
        # 否则使用普通的重写规则
        rewrite ^/dify/(.*)$ /$1 last;
    }

    # 捕获所有其他 /dify/ 的请求
    # 这个 location 块是普通请求的入口
    location /dify/ {
        # 使用 rewrite 指令将请求URI中的 /dify/ 前缀去掉
        # 例如: /dify/api/v1/workspaces -> /api/v1/workspaces
        # last 参数表示重写URL后，Nginx会重新开始匹配下面的 location 规则
        rewrite ^/dify/(.*)$ /$1 last;
    }
    # ================= 新增部分 结束 =================


    # ---- 以下是原有的 location 规则，保持不变 ----
    # Nginx重写URL后，会根据新的URL（已经没有/dify前缀）匹配到这些规则

    location /console/api {
      proxy_pass http://api:5001;
      include proxy.conf;
    }

    location /api {
      proxy_pass http://api:5001;
      include proxy.conf;
    }

    location /v1 {
      proxy_pass http://api:5001;
      include proxy.conf;
    }

    location /files {
      proxy_pass http://api:5001;
      include proxy.conf;
    }

    location /explore {
      proxy_pass http://web:3000;
      include proxy.conf;
    }

    location /e/ {
      proxy_pass http://plugin_daemon:5002;
      proxy_set_header Dify-Hook-Url $scheme://$host$request_uri;
      include proxy.conf;
    }

    location / {
      proxy_pass http://web:3000;
      include proxy.conf;
    }

    location /mcp {
      proxy_pass http://api:5001;
      include proxy.conf;
    }
    # placeholder for acme challenge location
    ${ACME_CHALLENGE_LOCATION}

    # placeholder for https config defined in https.conf.template
    ${HTTPS_CONFIG}
}
