import { useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { autoLogin } from '@/service/common'
import { useTranslation } from 'react-i18next'
import { useToastContext } from '@/app/components/base/toast'
type UseAutoLoginOptions = {
  onSuccess?: (accessToken: string, refreshToken: string) => void
  onError?: (error: string) => void
  redirectTo?: string
}

export const useAutoLogin = (options: UseAutoLoginOptions = {}) => {
  const router = useRouter()
  const { t } = useTranslation()
  const { notify } = useToastContext()
  const { onSuccess, onError, redirectTo = '/apps' } = options

  const performAutoLogin = useCallback(async (token: string, headers?: Record<string, any>) => {
    try {
      const res: any = await autoLogin({
        headers: {
          'Content-Type': 'application/json',
          'access-token': token,
          ...headers,
        },
      })
      if (res.result === 'success') {
        // 保存token到localStorage
        const { access_token, refresh_token } = res.data
        localStorage.setItem('console_token', access_token)
        localStorage.setItem('refresh_token', refresh_token)
        // 调用成功回调
        onSuccess?.(access_token, refresh_token)
        // 跳转到指定页面
        if (redirectTo)
          router.replace(redirectTo)
        return { success: true, data: res.data }
      }
      else {
        const errorMessage = res.message || t('login.autoLogin.failed')
        onError?.(errorMessage)
        notify({
          type: 'error',
          message: errorMessage,
        })
        localStorage.removeItem('console_token')
        localStorage.removeItem('refresh_token')
        // 跳转到登录页面
        router.replace('/signin')
        return { success: false, error: errorMessage }
      }
    }
    catch (error: any) {
      const errorMessage = error.message || t('login.autoLogin.error')
      onError?.(errorMessage)
      notify({
        type: 'error',
        message: errorMessage,
      })
      localStorage.removeItem('console_token')
      localStorage.removeItem('refresh_token')
      // 跳转到登录页面
      router.replace('/signin')
      return { success: false, error: errorMessage }
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [router, onSuccess, onError, redirectTo])

  return { performAutoLogin }
}
